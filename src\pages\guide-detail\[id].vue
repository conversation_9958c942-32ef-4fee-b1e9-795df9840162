<!-- 申报指南详情页 -->
<template>
  <div class="guide-detail-page">
    <div class="content" v-if="guideDetail.id">
      <!-- 指南头部 -->
      <div class="guide-header">
        <h1 class="title">{{ guideDetail.title }}</h1>
        <div class="meta-info">
          <span class="publish-time">{{ guideDetail.publishTime }}</span>
          <span class="view-count">
            <van-icon name="eye-o" />
            {{ guideDetail.viewCount }}
          </span>
        </div>
        <div class="tags">
          <van-tag type="primary" size="small">{{ guideDetail.categoryName }}</van-tag>
          <van-tag v-if="guideDetail.isRecommend" type="success" size="small">推荐</van-tag>
        </div>
      </div>

      <!-- 快速导航 -->
      <van-cell-group title="快速导航" v-if="guideDetail.sections">
        <van-cell
          v-for="section in guideDetail.sections"
          :key="section.id"
          :title="section.title"
          is-link
          @click="scrollToSection(section.id)"
        />
      </van-cell-group>

      <!-- 指南内容 -->
      <div class="guide-content">
        <div class="content-html" v-html="guideDetail.content"></div>
      </div>

      <!-- 申报材料清单 -->
      <van-cell-group v-if="guideDetail.materials" title="申报材料清单">
        <van-cell
          v-for="(material, index) in guideDetail.materials"
          :key="index"
          :title="material.name"
          :label="material.description"
        >
          <template #icon>
            <van-icon name="description" />
          </template>
          <template #right-icon>
            <van-tag v-if="material.required" type="danger" size="small">必需</van-tag>
            <van-tag v-else type="default" size="small">可选</van-tag>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 申报流程 -->
      <van-cell-group v-if="guideDetail.process" title="申报流程">
        <van-steps direction="vertical" :active="guideDetail.process.length">
          <van-step v-for="(step, index) in guideDetail.process" :key="index">
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
            <p v-if="step.duration" class="step-duration">预计用时：{{ step.duration }}</p>
          </van-step>
        </van-steps>
      </van-cell-group>

      <!-- 常见问题 -->
      <van-cell-group v-if="guideDetail.faq" title="常见问题">
        <van-collapse v-model="activeFaq">
          <van-collapse-item
            v-for="(item, index) in guideDetail.faq"
            :key="index"
            :title="item.question"
            :name="index"
          >
            <div v-html="item.answer"></div>
          </van-collapse-item>
        </van-collapse>
      </van-cell-group>

      <!-- 联系方式 -->
      <van-cell-group title="咨询联系">
        <van-cell
          title="咨询电话"
          :value="guideDetail.contactPhone || '************'"
          icon="phone-o"
          is-link
          @click="callPhone(guideDetail.contactPhone || '************')"
        />
        <van-cell
          title="咨询邮箱"
          :value="guideDetail.contactEmail || '<EMAIL>'"
          icon="envelop-o"
          is-link
          @click="sendEmail(guideDetail.contactEmail || '<EMAIL>')"
        />
        <van-cell
          title="办公地址"
          :value="guideDetail.address || '某某市政务服务中心'"
          icon="location-o"
          is-link
          @click="openMap"
        />
      </van-cell-group>

      <!-- 相关指南推荐 -->
      <van-cell-group v-if="relatedGuides.length > 0" title="相关指南">
        <van-cell
          v-for="item in relatedGuides"
          :key="item.id"
          :title="item.title"
          :label="item.publishTime"
          is-link
          @click="goToGuide(item)"
        />
      </van-cell-group>

      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <van-button 
          icon="star-o" 
          :class="{ 'is-collected': guideDetail.isCollected }"
          @click="toggleCollect"
        >
          {{ guideDetail.isCollected ? '已收藏' : '收藏' }}
        </van-button>
        <van-button icon="share-o" @click="shareGuide">
          分享
        </van-button>
        <van-button 
          type="primary" 
          @click="goToApply"
          v-if="guideDetail.canApply"
        >
          立即申报
        </van-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-wrapper" size="24px">
      加载中...
    </van-loading>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showShareSheet } from 'vant'

const route = useRoute()
const router = useRouter()

// 响应式数据
const guideDetail = ref({})
const relatedGuides = ref([])
const activeFaq = ref([])

// 方法
const fetchGuideDetail = async () => {
  try {
    const guideId = route.params.id
    
    // TODO: 调用API获取指南详情
    // const response = await guideApi.getGuideDetail(guideId)
    // guideDetail.value = response
    
    // 模拟数据
    guideDetail.value = {
      id: guideId,
      title: '购房补贴申报完整指南',
      content: `
        <div id="section-1">
          <h2>申报条件</h2>
          <p>申请购房补贴需要满足以下基本条件：</p>
          <ul>
            <li>在本市购买首套住房的家庭</li>
            <li>购房时间在政策有效期内</li>
            <li>购房面积符合规定标准</li>
            <li>家庭收入符合相关要求</li>
          </ul>
        </div>
        
        <div id="section-2">
          <h2>补贴标准</h2>
          <p>根据购房面积和价格，给予不同标准的补贴：</p>
          <table border="1" style="width:100%; border-collapse: collapse;">
            <tr>
              <th>房屋面积</th>
              <th>补贴比例</th>
              <th>最高限额</th>
            </tr>
            <tr>
              <td>90㎡以下</td>
              <td>房价的2%</td>
              <td>10万元</td>
            </tr>
            <tr>
              <td>90-120㎡</td>
              <td>房价的1.5%</td>
              <td>8万元</td>
            </tr>
            <tr>
              <td>120-144㎡</td>
              <td>房价的1%</td>
              <td>6万元</td>
            </tr>
          </table>
        </div>
        
        <div id="section-3">
          <h2>注意事项</h2>
          <p>申报过程中需要注意以下事项：</p>
          <ul>
            <li>所有材料必须真实有效</li>
            <li>申报时间不得超过购房后6个月</li>
            <li>同一家庭只能享受一次补贴</li>
          </ul>
        </div>
      `,
      categoryName: '购房补贴',
      isRecommend: true,
      publishTime: '2024-01-15',
      viewCount: 2156,
      isCollected: false,
      canApply: true,
      sections: [
        { id: 'section-1', title: '申报条件' },
        { id: 'section-2', title: '补贴标准' },
        { id: 'section-3', title: '注意事项' }
      ],
      materials: [
        {
          name: '身份证',
          description: '申请人及配偶身份证复印件',
          required: true
        },
        {
          name: '购房合同',
          description: '经备案的购房合同原件及复印件',
          required: true
        },
        {
          name: '不动产证',
          description: '不动产权证书复印件',
          required: true
        },
        {
          name: '银行流水',
          description: '近6个月银行流水证明',
          required: false
        }
      ],
      process: [
        {
          title: '准备材料',
          description: '按照材料清单准备所有申报材料',
          duration: '1-2天'
        },
        {
          title: '在线申报',
          description: '登录系统填写申报信息并上传材料',
          duration: '30分钟'
        },
        {
          title: '部门审核',
          description: '相关部门对申报材料进行审核',
          duration: '5-10个工作日'
        },
        {
          title: '公示发放',
          description: '审核通过后公示并发放补贴',
          duration: '7个工作日'
        }
      ],
      faq: [
        {
          question: '购房补贴可以重复申请吗？',
          answer: '不可以。同一家庭只能享受一次购房补贴政策。'
        },
        {
          question: '申报时间有限制吗？',
          answer: '有限制。必须在购房后6个月内提出申请，超过时间将无法受理。'
        },
        {
          question: '补贴资金多久能到账？',
          answer: '审核通过并公示无异议后，补贴资金将在10个工作日内发放到指定账户。'
        }
      ],
      contactPhone: '************',
      contactEmail: '<EMAIL>',
      address: '某某市政务服务中心2楼'
    }

    // 获取相关指南
    relatedGuides.value = [
      {
        id: 2,
        title: '高层次人才认定申报指南',
        publishTime: '2024-01-12'
      },
      {
        id: 3,
        title: '创业扶持资金申请指南',
        publishTime: '2024-01-10'
      }
    ]
  } catch (error) {
    console.error('获取指南详情失败:', error)
    showToast('获取详情失败')
  }
}

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const callPhone = (phone) => {
  window.location.href = `tel:${phone}`
}

const sendEmail = (email) => {
  window.location.href = `mailto:${email}`
}

const openMap = () => {
  showToast('打开地图导航')
}

const goToGuide = (guide) => {
  router.push({
    name: 'guide-detail',
    params: { id: guide.id }
  })
}

const goToApply = () => {
  router.push({ name: 'subsidy-apply' })
}

const toggleCollect = async () => {
  try {
    // TODO: 调用收藏/取消收藏API
    guideDetail.value.isCollected = !guideDetail.value.isCollected
    showToast(guideDetail.value.isCollected ? '收藏成功' : '取消收藏')
  } catch (error) {
    showToast('操作失败')
  }
}

const shareGuide = () => {
  showShareSheet({
    title: '分享指南',
    options: [
      { name: '微信', icon: 'wechat' },
      { name: '朋友圈', icon: 'wechat-moments' },
      { name: '复制链接', icon: 'link' }
    ],
    onSelect: (option) => {
      showToast(`分享到${option.name}`)
    }
  })
}

// 生命周期
onMounted(() => {
  fetchGuideDetail()
})
</script>

<style lang="scss" scoped>
.guide-detail-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
  padding-bottom: 80px; /* 为底部操作栏预留空间 */
}

.content {
  background: white;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.guide-header {
  padding: 20px;
  border-bottom: 1px solid var(--van-border-color);
}

.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 12px;
  color: var(--van-text-color);
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tags {
  display: flex;
  gap: 8px;
}

.guide-content {
  padding: 20px;
}

.content-html {
  line-height: 1.6;
  color: var(--van-text-color);
  
  :deep(h2) {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 12px;
    color: var(--van-text-color);
  }
  
  :deep(p) {
    margin-bottom: 12px;
  }
  
  :deep(ul) {
    padding-left: 20px;
    margin-bottom: 12px;
  }
  
  :deep(li) {
    margin-bottom: 6px;
  }
  
  :deep(table) {
    width: 100%;
    margin: 12px 0;
    border-collapse: collapse;
    
    th, td {
      padding: 8px;
      text-align: center;
      border: 1px solid var(--van-border-color);
    }
    
    th {
      background-color: var(--van-background-2);
      font-weight: 600;
    }
  }
}

.step-duration {
  font-size: 12px;
  color: var(--van-text-color-3);
  margin-top: 4px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--van-border-color);
  display: flex;
  gap: 12px;
  align-items: center;
  
  .van-button {
    flex: 1;
  }
  
  .is-collected {
    color: var(--van-primary-color);
  }
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

<route lang="json5">
{
  name: 'guide-detail',
  meta: {
    title: '申报指南'
  }
}
</route>
