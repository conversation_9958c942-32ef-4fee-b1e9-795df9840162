<!-- 个人信息页面 -->
<template>
  <div class="user-info-page">
    <div class="content">
      <!-- 头像区域 -->
      <div class="avatar-section">
        <van-uploader
          v-model="avatarList"
          :max-count="1"
          :preview-size="80"
          upload-icon="photo"
          @after-read="onAvatarChange"
        >
          <van-avatar 
            v-if="userInfo.avatar" 
            :src="userInfo.avatar" 
            size="80"
          />
          <van-avatar 
            v-else
            icon="user-o" 
            size="80"
          />
        </van-uploader>
        <p class="avatar-tip">点击更换头像</p>
      </div>

      <!-- 基本信息 -->
      <van-cell-group title="基本信息">
        <van-field
          v-model="userInfo.name"
          label="姓名"
          placeholder="请输入姓名"
          required
          :readonly="!isEditing"
        />
        <van-field
          v-model="userInfo.phone"
          label="手机号"
          placeholder="请输入手机号"
          type="tel"
          required
          :readonly="!isEditing"
        />
        <van-field
          v-model="userInfo.idCard"
          label="身份证号"
          placeholder="请输入身份证号"
          required
          :readonly="!isEditing"
        />
        <van-field
          v-model="userInfo.email"
          label="邮箱"
          placeholder="请输入邮箱"
          type="email"
          :readonly="!isEditing"
        />
      </van-cell-group>

      <!-- 地址信息 -->
      <van-cell-group title="地址信息">
        <van-field
          v-model="userInfo.address"
          label="联系地址"
          placeholder="请输入详细地址"
          type="textarea"
          rows="3"
          :readonly="!isEditing"
        />
      </van-cell-group>

      <!-- 认证信息 -->
      <van-cell-group title="认证信息">
        <van-cell title="实名认证" :value="userInfo.isVerified ? '已认证' : '未认证'">
          <template #right-icon>
            <van-tag v-if="userInfo.isVerified" type="success" size="small">已认证</van-tag>
            <van-tag v-else type="warning" size="small">未认证</van-tag>
          </template>
        </van-cell>
        <van-cell 
          title="认证时间" 
          :value="userInfo.verifyTime || '未认证'" 
          v-if="userInfo.isVerified"
        />
      </van-cell-group>

      <!-- 账户安全 -->
      <van-cell-group title="账户安全">
        <van-cell
          title="修改密码"
          is-link
          @click="goToChangePassword"
        />
        <van-cell
          title="绑定手机"
          :value="userInfo.phone ? '已绑定' : '未绑定'"
          is-link
          @click="goToBindPhone"
        />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button 
          v-if="!isEditing"
          type="primary" 
          block 
          @click="startEdit"
        >
          编辑信息
        </van-button>
        <div v-else class="edit-buttons">
          <van-button @click="cancelEdit">取消</van-button>
          <van-button type="primary" @click="saveUserInfo">保存</van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()

// 响应式数据
const isEditing = ref(false)
const avatarList = ref([])
const userInfo = reactive({
  name: '',
  phone: '',
  idCard: '',
  email: '',
  address: '',
  avatar: '',
  isVerified: false,
  verifyTime: ''
})

// 备份原始数据
const originalUserInfo = ref({})

// 方法
const fetchUserInfo = async () => {
  try {
    // TODO: 调用API获取用户信息
    // const response = await userApi.getUserInfo()
    // Object.assign(userInfo, response)
    
    // 模拟数据
    Object.assign(userInfo, {
      name: '张三',
      phone: '13888888888',
      idCard: '330***********1234',
      email: '<EMAIL>',
      address: '浙江省杭州市西湖区某某街道123号',
      avatar: '',
      isVerified: true,
      verifyTime: '2024-01-10 14:30:00'
    })
  } catch (error) {
    console.error('获取用户信息失败:', error)
    showToast('获取用户信息失败')
  }
}

const startEdit = () => {
  isEditing.value = true
  originalUserInfo.value = { ...userInfo }
}

const cancelEdit = () => {
  isEditing.value = false
  Object.assign(userInfo, originalUserInfo.value)
}

const saveUserInfo = async () => {
  try {
    // 验证必填字段
    if (!userInfo.name || !userInfo.phone || !userInfo.idCard) {
      showToast('请填写完整的基本信息')
      return
    }

    // TODO: 调用API保存用户信息
    // await userApi.updateUserInfo(userInfo)
    
    isEditing.value = false
    showToast('保存成功')
  } catch (error) {
    console.error('保存用户信息失败:', error)
    showToast('保存失败，请重试')
  }
}

const onAvatarChange = async (file) => {
  try {
    // TODO: 上传头像
    // const formData = new FormData()
    // formData.append('avatar', file.file)
    // const response = await userApi.uploadAvatar(formData)
    // userInfo.avatar = response.url
    
    showToast('头像上传成功')
  } catch (error) {
    console.error('头像上传失败:', error)
    showToast('头像上传失败')
  }
}

const goToChangePassword = () => {
  router.push({ name: 'change-password' })
}

const goToBindPhone = () => {
  router.push({ name: 'bind-phone' })
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.user-info-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
  background: white;
  border-radius: var(--house-border-radius);
  margin-bottom: 16px;
  
  .van-uploader {
    margin-bottom: 8px;
  }
  
  .avatar-tip {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin: 0;
  }
}

.van-cell-group {
  margin-bottom: 16px;
}

.action-buttons {
  margin-top: 24px;
}

.edit-buttons {
  display: flex;
  gap: 16px;
  
  .van-button {
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'user-info',
  meta: {
    title: '个人信息'
  }
}
</route>
