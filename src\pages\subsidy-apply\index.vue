<!-- 补贴申请 -->
<template>
  <div class="subsidy-apply-page">
    <div class="content">
      <!-- 步骤指示器 -->
      <van-steps :active="currentStep" class="steps">
        <van-step>选择合同</van-step>
        <van-step>确认交易信息</van-step>
        <van-step>确认收款账号</van-step>
      </van-steps>

      <!-- 步骤1: 选择合同 -->
      <div v-if="currentStep === 0" class="step-content">
        <van-cell-group title="请选择购房合同">
          <van-field
            v-model="contractNumber"
            label="合同编号"
            placeholder="请输入合同编号"
            required
          />
          <van-field
            v-model="contractDate"
            label="签约日期"
            placeholder="请选择签约日期"
            readonly
            is-link
            @click="showDatePicker = true"
          />
        </van-cell-group>

        <van-cell-group title="合同信息" v-if="contractInfo.id">
          <van-cell title="房屋地址" :value="contractInfo.address" />
          <van-cell title="房屋面积" :value="contractInfo.area + '㎡'" />
          <van-cell title="成交价格" :value="contractInfo.price + '万元'" />
          <van-cell title="购房人" :value="contractInfo.buyer" />
        </van-cell-group>

        <div class="step-actions">
          <van-button 
            type="primary" 
            block 
            @click="nextStep"
            :disabled="!contractNumber || !contractDate"
          >
            下一步
          </van-button>
        </div>
      </div>

      <!-- 步骤2: 确认交易信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <!-- 房屋交易信息 -->
        <van-cell-group title="房屋交易信息">
          <van-cell title="房屋地址" :value="transactionInfo.address" />
          <van-cell title="房屋面积" :value="transactionInfo.area + '㎡'" />
          <van-cell title="成交价格" :value="transactionInfo.price + '万元'" />
          <van-cell title="交易时间" :value="transactionInfo.date" />
        </van-cell-group>

        <!-- 不动产信息 -->
        <van-cell-group title="不动产信息">
          <van-cell title="不动产证号" :value="propertyInfo.certificateNo" />
          <van-cell title="房屋性质" :value="propertyInfo.type" />
          <van-cell title="土地使用权类型" :value="propertyInfo.landType" />
        </van-cell-group>

        <!-- 契税信息 -->
        <van-cell-group title="契税信息">
          <van-cell title="契税金额" :value="taxInfo.amount + '元'" />
          <van-cell title="缴税时间" :value="taxInfo.payTime" />
          <van-cell title="税务机关" :value="taxInfo.office" />
        </van-cell-group>

        <div class="step-actions">
          <van-button @click="prevStep">上一步</van-button>
          <van-button type="primary" @click="nextStep">下一步</van-button>
        </div>
      </div>

      <!-- 步骤3: 确认收款账号 -->
      <div v-if="currentStep === 2" class="step-content">
        <van-cell-group title="收款账号信息">
          <van-field
            v-model="accountInfo.bankName"
            label="开户银行"
            placeholder="请输入开户银行"
            required
          />
          <van-field
            v-model="accountInfo.accountNumber"
            label="银行账号"
            placeholder="请输入银行账号"
            required
          />
          <van-field
            v-model="accountInfo.accountName"
            label="账户姓名"
            placeholder="请输入账户姓名"
            required
          />
        </van-cell-group>

        <div class="step-actions">
          <van-button @click="prevStep">上一步</van-button>
          <van-button type="primary" @click="showConfirmDialog">确认信息</van-button>
        </div>
      </div>

      <!-- 日期选择器 -->
      <van-popup v-model:show="showDatePicker" position="bottom">
        <van-date-picker
          v-model="selectedDate"
          @confirm="onDateConfirm"
          @cancel="showDatePicker = false"
        />
      </van-popup>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showToast } from 'vant'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const showDatePicker = ref(false)
const selectedDate = ref(new Date())

// 表单数据
const contractNumber = ref('')
const contractDate = ref('')

const contractInfo = reactive({
  id: '',
  address: '',
  area: '',
  price: '',
  buyer: ''
})

const transactionInfo = reactive({
  address: '某某小区1号楼101室',
  area: '120',
  price: '300',
  date: '2024-01-15'
})

const propertyInfo = reactive({
  certificateNo: '浙（2024）某某不动产权第0001号',
  type: '商品房',
  landType: '出让'
})

const taxInfo = reactive({
  amount: '9000',
  payTime: '2024-01-16',
  office: '某某区税务局'
})

const accountInfo = reactive({
  bankName: '',
  accountNumber: '',
  accountName: ''
})

// 方法
const nextStep = () => {
  if (currentStep.value === 0) {
    // 验证合同信息
    fetchContractInfo()
  }
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const fetchContractInfo = async () => {
  try {
    // TODO: 根据合同编号获取合同信息
    // const response = await contractApi.getContractInfo(contractNumber.value)
    // Object.assign(contractInfo, response)
    
    // 模拟数据
    Object.assign(contractInfo, {
      id: '1',
      address: '某某小区1号楼101室',
      area: '120',
      price: '300',
      buyer: '张三'
    })
  } catch (error) {
    showToast('获取合同信息失败')
  }
}

const onDateConfirm = (value) => {
  contractDate.value = value.toISOString().split('T')[0]
  showDatePicker.value = false
}

const showConfirmDialog = async () => {
  try {
    await showConfirmDialog({
      title: '确认提交',
      message: '请确认所有信息无误后提交审核'
    })
    
    await submitApplication()
  } catch (error) {
    // 用户取消
  }
}

const submitApplication = async () => {
  try {
    // TODO: 提交申请
    // await subsidyApi.submitApplication({
    //   contractNumber: contractNumber.value,
    //   contractDate: contractDate.value,
    //   accountInfo: accountInfo
    // })
    
    showToast('提交成功，请等待审核')
    router.push({ name: 'progress' })
  } catch (error) {
    showToast('提交失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.subsidy-apply-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}

.steps {
  margin-bottom: 24px;
}

.step-content {
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.step-actions {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  
  .van-button {
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'subsidy-apply',
  meta: {
    title: '补贴申请'
  }
}
</route>
