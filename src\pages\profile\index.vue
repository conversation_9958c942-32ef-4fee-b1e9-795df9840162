<!-- 我的页面 -->
<template>
  <div class="profile-page">
    <div class="content">
      <!-- 个人信息卡片 -->
      <van-cell-group class="user-info-card">
        <van-cell 
          :title="userInfo.name || '未登录'"
          :label="userInfo.phone || '请登录'"
          size="large"
          is-link
          @click="goToUserInfo"
        >
          <template #icon>
            <van-avatar 
              :src="userInfo.avatar" 
              size="50"
              icon="user-o"
            />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 功能菜单 -->
      <van-cell-group class="menu-group">
        <van-cell
          title="补贴申报记录"
          icon="orders-o"
          is-link
          @click="goToRecords"
        />
        <van-cell
          title="个人信息"
          icon="contact"
          is-link
          @click="goToUserInfo"
        />
        <van-cell
          title="设置"
          icon="setting-o"
          is-link
          @click="goToSettings"
        />
        <van-cell
          title="帮助与反馈"
          icon="question-o"
          is-link
          @click="goToHelp"
        />
      </van-cell-group>

      <!-- 退出登录 -->
      <div class="logout-section">
        <van-button 
          type="danger" 
          block 
          @click="handleLogout"
          v-if="userInfo.name"
        >
          退出登录
        </van-button>
        <van-button 
          type="primary" 
          block 
          @click="handleLogin"
          v-else
        >
          立即登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showToast } from 'vant'

const router = useRouter()

// 响应式数据
const userInfo = ref({
  name: '',
  phone: '',
  avatar: ''
})

// 方法
const fetchUserInfo = async () => {
  try {
    // TODO: 调用API获取用户信息
    // const response = await userApi.getUserInfo()
    // userInfo.value = response
    
    // 模拟数据
    userInfo.value = {
      name: '张三',
      phone: '138****8888',
      avatar: ''
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

const goToRecords = () => {
  router.push({ name: 'progress' })
}

const goToUserInfo = () => {
  router.push({ name: 'user-info' })
}

const goToSettings = () => {
  showToast('功能开发中')
}

const goToHelp = () => {
  showToast('功能开发中')
}

const handleLogin = () => {
  router.push({ name: 'login' })
}

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '提示',
      message: '确定要退出登录吗？'
    })
    
    // TODO: 调用退出登录API
    // await userApi.logout()
    
    userInfo.value = {
      name: '',
      phone: '',
      avatar: ''
    }
    
    showToast('已退出登录')
  } catch (error) {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})

onActivated(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}

.user-info-card {
  margin-bottom: 16px;
  
  .van-cell {
    padding: 20px 16px;
  }
  
  .van-avatar {
    margin-right: 12px;
  }
}

.menu-group {
  margin-bottom: 24px;
}

.logout-section {
  padding: 0 16px;
}
</style>

<route lang="json5">
{
  name: 'profile',
  meta: {
    title: '我的'
  }
}
</route>
