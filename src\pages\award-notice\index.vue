<!-- 奖补公示列表页 -->
<template>
  <div class="award-notice-page">
    <div class="content">
      <!-- 搜索栏 -->
      <van-search
        v-model="searchKeyword"
        placeholder="搜索公示信息"
        @search="onSearch"
        @clear="onClear"
      />

      <!-- 公示分类 -->
      <van-tabs v-model:active="activeCategory" @change="onCategoryChange">
        <van-tab 
          v-for="category in categoryList" 
          :key="category.id"
          :title="category.name"
        />
      </van-tabs>

      <!-- 公示列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        class="notice-list"
      >
        <van-card
          v-for="item in noticeList"
          :key="item.id"
          :title="item.title"
          :desc="item.summary"
          :thumb="item.cover"
          @click="goToDetail(item)"
        >
          <template #tags>
            <van-tag type="success" size="small">{{ item.categoryName }}</van-tag>
            <van-tag v-if="item.status === 'ongoing'" type="warning" size="small">公示中</van-tag>
            <van-tag v-else type="default" size="small">已结束</van-tag>
          </template>
          <template #footer>
            <div class="card-footer">
              <span class="publish-time">{{ item.publishTime }}</span>
              <div class="actions">
                <van-icon name="eye-o" />
                <span>{{ item.viewCount }}</span>
              </div>
            </div>
          </template>
        </van-card>
      </van-list>

      <!-- 空状态 -->
      <van-empty 
        v-if="noticeList.length === 0 && !loading"
        description="暂无公示信息" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeCategory = ref(0)
const noticeList = ref([])
const categoryList = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '购房补贴' },
  { id: 2, name: '人才奖励' },
  { id: 3, name: '创业扶持' }
])

// 分页参数
const pageParams = ref({
  current: 1,
  size: 10
})

// 方法
const fetchNoticeList = async (reset = false) => {
  if (reset) {
    pageParams.value.current = 1
    noticeList.value = []
    finished.value = false
  }

  loading.value = true
  try {
    // TODO: 调用API获取公示列表
    // const response = await awardApi.getNoticeList({
    //   ...pageParams.value,
    //   keyword: searchKeyword.value,
    //   categoryId: categoryList.value[activeCategory.value].id
    // })
    
    // 模拟数据
    const mockData = [
      {
        id: 1,
        title: '2024年第一批购房补贴发放公示',
        summary: '经审核，以下人员符合购房补贴发放条件，现予以公示...',
        cover: '/src/assets/bg.png',
        categoryName: '购房补贴',
        status: 'ongoing',
        publishTime: '2024-01-15',
        viewCount: 856
      },
      {
        id: 2,
        title: '高层次人才奖励资金发放公示',
        summary: '根据人才引进政策，对符合条件的高层次人才给予奖励资金...',
        cover: '/src/assets/bg.png',
        categoryName: '人才奖励',
        status: 'ended',
        publishTime: '2024-01-10',
        viewCount: 642
      },
      {
        id: 3,
        title: '创业扶持资金补贴名单公示',
        summary: '经评审，以下创业项目获得扶持资金补贴，现予以公示...',
        cover: '/src/assets/bg.png',
        categoryName: '创业扶持',
        status: 'ongoing',
        publishTime: '2024-01-08',
        viewCount: 423
      }
    ]

    if (reset) {
      noticeList.value = mockData
    } else {
      noticeList.value.push(...mockData)
    }

    pageParams.value.current++
    
    // 模拟没有更多数据
    if (pageParams.value.current > 2) {
      finished.value = true
    }
  } catch (error) {
    console.error('获取公示列表失败:', error)
  } finally {
    loading.value = false
  }
}

const onLoad = () => {
  fetchNoticeList()
}

const onSearch = () => {
  fetchNoticeList(true)
}

const onClear = () => {
  searchKeyword.value = ''
  fetchNoticeList(true)
}

const onCategoryChange = () => {
  fetchNoticeList(true)
}

const goToDetail = (item) => {
  router.push({
    name: 'award-notice-detail',
    params: { id: item.id }
  })
}

// 生命周期
onMounted(() => {
  fetchNoticeList(true)
})

onActivated(() => {
  fetchNoticeList(true)
})
</script>

<style lang="scss" scoped>
.award-notice-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding-bottom: 16px;
}

.van-search {
  padding: 16px;
  background: white;
}

.notice-list {
  padding: 0 16px;
  
  .van-card {
    margin-bottom: 16px;
    
    :deep(.van-card__content) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.publish-time {
  color: var(--van-text-color-3);
}
</style>

<route lang="json5">
{
  name: 'award-notice',
  meta: {
    title: '奖补公示'
  }
}
</route>
