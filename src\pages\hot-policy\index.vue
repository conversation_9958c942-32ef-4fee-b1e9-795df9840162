<!-- 热门政策列表页 -->
<template>
  <div class="hot-policy-page">
    <div class="content">
      <!-- 搜索栏 -->
      <van-search
        v-model="searchKeyword"
        placeholder="搜索政策"
        @search="onSearch"
        @clear="onClear"
      />

      <!-- 政策分类 -->
      <van-tabs v-model:active="activeCategory" @change="onCategoryChange">
        <van-tab 
          v-for="category in categoryList" 
          :key="category.id"
          :title="category.name"
        />
      </van-tabs>

      <!-- 政策列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        class="policy-list"
      >
        <van-card
          v-for="item in policyList"
          :key="item.id"
          :title="item.title"
          :desc="item.summary"
          :thumb="item.cover"
          @click="goToDetail(item)"
        >
          <template #tags>
            <van-tag type="primary" size="small">{{ item.categoryName }}</van-tag>
          </template>
          <template #footer>
            <div class="card-footer">
              <span class="publish-time">{{ item.publishTime }}</span>
              <div class="actions">
                <van-icon name="eye-o" />
                <span>{{ item.viewCount }}</span>
              </div>
            </div>
          </template>
        </van-card>
      </van-list>

      <!-- 空状态 -->
      <van-empty 
        v-if="policyList.length === 0 && !loading"
        description="暂无政策信息" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeCategory = ref(0)
const policyList = ref([])
const categoryList = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '购房补贴' },
  { id: 2, name: '人才政策' },
  { id: 3, name: '税收优惠' }
])

// 分页参数
const pageParams = ref({
  current: 1,
  size: 10
})

// 方法
const fetchPolicyList = async (reset = false) => {
  if (reset) {
    pageParams.value.current = 1
    policyList.value = []
    finished.value = false
  }

  loading.value = true
  try {
    // TODO: 调用API获取政策列表
    // const response = await policyApi.getPolicyList({
    //   ...pageParams.value,
    //   keyword: searchKeyword.value,
    //   categoryId: categoryList.value[activeCategory.value].id
    // })
    
    // 模拟数据
    const mockData = [
      {
        id: 1,
        title: '2024年购房补贴政策实施细则',
        summary: '为进一步促进房地产市场健康发展，现制定购房补贴政策实施细则...',
        cover: '/src/assets/bg.png',
        categoryName: '购房补贴',
        publishTime: '2024-01-15',
        viewCount: 1234
      },
      {
        id: 2,
        title: '高层次人才购房优惠政策',
        summary: '为吸引高层次人才，对符合条件的人才给予购房补贴和税收优惠...',
        cover: '/src/assets/bg.png',
        categoryName: '人才政策',
        publishTime: '2024-01-10',
        viewCount: 856
      },
      {
        id: 3,
        title: '首套房契税减免政策解读',
        summary: '对首次购买住房的家庭，契税享受减免优惠，具体标准如下...',
        cover: '/src/assets/bg.png',
        categoryName: '税收优惠',
        publishTime: '2024-01-08',
        viewCount: 692
      }
    ]

    if (reset) {
      policyList.value = mockData
    } else {
      policyList.value.push(...mockData)
    }

    pageParams.value.current++
    
    // 模拟没有更多数据
    if (pageParams.value.current > 2) {
      finished.value = true
    }
  } catch (error) {
    console.error('获取政策列表失败:', error)
  } finally {
    loading.value = false
  }
}

const onLoad = () => {
  fetchPolicyList()
}

const onSearch = () => {
  fetchPolicyList(true)
}

const onClear = () => {
  searchKeyword.value = ''
  fetchPolicyList(true)
}

const onCategoryChange = () => {
  fetchPolicyList(true)
}

const goToDetail = (item) => {
  router.push({
    name: 'policy-detail',
    params: { id: item.id }
  })
}

// 生命周期
onMounted(() => {
  fetchPolicyList(true)
})

onActivated(() => {
  fetchPolicyList(true)
})
</script>

<style lang="scss" scoped>
.hot-policy-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding-bottom: 16px;
}

.van-search {
  padding: 16px;
  background: white;
}

.policy-list {
  padding: 0 16px;
  
  .van-card {
    margin-bottom: 16px;
    
    :deep(.van-card__content) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.publish-time {
  color: var(--van-text-color-3);
}
</style>

<route lang="json5">
{
  name: 'hot-policy',
  meta: {
    title: '热门政策'
  }
}
</route>
