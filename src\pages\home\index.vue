<!-- 首页 -->
<template>
  <div class="home-page">
    <div class="content">
      <!-- 轮播图 -->
      <van-swipe class="banner-swipe" :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="(banner, index) in bannerList" :key="index">
          <img :src="banner.image" :alt="banner.title" />
        </van-swipe-item>
      </van-swipe>

      <!-- 功能模块 -->
      <div class="function-grid">
        <div
          class="function-item"
          v-for="item in functionList"
          :key="item.name"
          @click="goToFunction(item)"
        >
          <div class="function-icon">
            <van-icon :name="item.icon" size="32" />
          </div>
          <div class="function-title">{{ item.title }}</div>
        </div>
      </div>

      <!-- 快捷入口 -->
      <van-cell-group class="quick-entry" title="快捷入口">
        <van-cell
          title="我的申请"
          icon="orders-o"
          is-link
          @click="goToProgress"
        />
        <van-cell
          title="政策咨询"
          icon="phone-o"
          is-link
          @click="showConsult"
        />
      </van-cell-group>

      <!-- 最新公告 -->
      <van-cell-group class="notice-section" title="最新公告">
        <van-cell
          v-for="notice in noticeList"
          :key="notice.id"
          :title="notice.title"
          :label="notice.publishTime"
          is-link
          @click="goToNoticeDetail(notice)"
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const bannerList = ref([])
const functionList = ref([
  {
    name: 'subsidy-apply',
    title: '补贴申请',
    icon: 'gold-coin-o'
  },
  {
    name: 'hot-policy',
    title: '热门政策',
    icon: 'fire-o'
  },
  {
    name: 'award-notice',
    title: '奖补公示',
    icon: 'award-o'
  },
  {
    name: 'apply-guide',
    title: '申报指南',
    icon: 'guide-o'
  }
])
const noticeList = ref([])

// 方法
const fetchData = async () => {
  try {
    // TODO: 调用API获取首页数据
    // const [bannerRes, noticeRes] = await Promise.all([
    //   homeApi.getBannerList(),
    //   homeApi.getNoticeList()
    // ])
    // bannerList.value = bannerRes.list
    // noticeList.value = noticeRes.list

    // 模拟数据
    bannerList.value = [
      {
        id: 1,
        title: '购房补贴政策',
        image: '/src/assets/bg.png'
      }
    ]

    noticeList.value = [
      {
        id: 1,
        title: '关于2024年购房补贴申请的通知',
        publishTime: '2024-01-15'
      },
      {
        id: 2,
        title: '购房补贴政策解读',
        publishTime: '2024-01-10'
      }
    ]
  } catch (error) {
    console.error('获取首页数据失败:', error)
  }
}

const goToFunction = (item) => {
  switch (item.name) {
    case 'subsidy-apply':
      router.push({ name: 'subsidy-apply' })
      break
    case 'hot-policy':
      router.push({ name: 'hot-policy' })
      break
    case 'award-notice':
      router.push({ name: 'award-notice' })
      break
    case 'apply-guide':
      router.push({ name: 'apply-guide' })
      break
    default:
      showToast('功能开发中')
  }
}

const goToProgress = () => {
  router.push({ name: 'progress' })
}

const showConsult = () => {
  showToast('请拨打咨询电话：400-123-4567')
}

const goToNoticeDetail = (notice) => {
  router.push({
    name: 'notice-detail',
    params: { id: notice.id }
  })
}

// 生命周期
onMounted(() => {
  fetchData()
})

onActivated(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding-bottom: 16px;
}

.banner-swipe {
  height: 200px;
  margin-bottom: 16px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  margin-bottom: 16px;
}

.function-item {
  background: white;
  border-radius: 8px;
  padding: 24px 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.95);
  }
}

.function-icon {
  margin-bottom: 8px;
  color: var(--van-primary-color);
}

.function-title {
  font-size: 14px;
  color: var(--van-text-color);
}

.quick-entry,
.notice-section {
  margin: 0 16px 16px;
}
</style>

<route lang="json5">
{
  name: 'home',
  meta: {
    title: '首页'
  }
}
</route>