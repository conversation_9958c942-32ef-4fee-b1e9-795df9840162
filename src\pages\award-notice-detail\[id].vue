<!-- 奖补公示详情页 -->
<template>
  <div class="award-notice-detail-page">
    <div class="content" v-if="noticeDetail.id">
      <!-- 公示头部 -->
      <div class="notice-header">
        <h1 class="title">{{ noticeDetail.title }}</h1>
        <div class="meta-info">
          <span class="publish-time">{{ noticeDetail.publishTime }}</span>
          <span class="view-count">
            <van-icon name="eye-o" />
            {{ noticeDetail.viewCount }}
          </span>
        </div>
        <div class="tags">
          <van-tag type="success" size="small">{{ noticeDetail.categoryName }}</van-tag>
          <van-tag 
            :type="noticeDetail.status === 'ongoing' ? 'warning' : 'default'" 
            size="small"
          >
            {{ noticeDetail.status === 'ongoing' ? '公示中' : '已结束' }}
          </van-tag>
        </div>
        
        <!-- 公示期限 -->
        <div class="notice-period" v-if="noticeDetail.publicityPeriod">
          <van-notice-bar
            :text="`公示期限：${noticeDetail.publicityPeriod.start} 至 ${noticeDetail.publicityPeriod.end}`"
            mode="closeable"
            background="#fff7e6"
            color="#fa8c16"
          />
        </div>
      </div>

      <!-- 公示内容 -->
      <div class="notice-content">
        <div class="content-html" v-html="noticeDetail.content"></div>
      </div>

      <!-- 公示名单 -->
      <van-cell-group v-if="noticeDetail.awardList && noticeDetail.awardList.length > 0" title="公示名单">
        <van-collapse v-model="activeNames">
          <van-collapse-item title="查看详细名单" name="1">
            <div class="award-list">
              <van-cell
                v-for="(item, index) in noticeDetail.awardList"
                :key="index"
                :title="item.name"
                :label="item.reason"
                :value="item.amount"
              />
            </div>
          </van-collapse-item>
        </van-collapse>
      </van-cell-group>

      <!-- 监督举报 -->
      <van-cell-group title="监督举报">
        <van-cell
          title="举报电话"
          :value="noticeDetail.reportPhone || '12345'"
          icon="phone-o"
          is-link
          @click="callPhone(noticeDetail.reportPhone || '12345')"
        />
        <van-cell
          title="举报邮箱"
          :value="noticeDetail.reportEmail || '<EMAIL>'"
          icon="envelop-o"
          is-link
          @click="sendEmail(noticeDetail.reportEmail || '<EMAIL>')"
        />
      </van-cell-group>

      <!-- 相关公示推荐 -->
      <van-cell-group v-if="relatedNotices.length > 0" title="相关公示">
        <van-cell
          v-for="item in relatedNotices"
          :key="item.id"
          :title="item.title"
          :label="item.publishTime"
          is-link
          @click="goToNotice(item)"
        />
      </van-cell-group>

      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <van-button 
          icon="star-o" 
          :class="{ 'is-collected': noticeDetail.isCollected }"
          @click="toggleCollect"
        >
          {{ noticeDetail.isCollected ? '已收藏' : '收藏' }}
        </van-button>
        <van-button icon="share-o" @click="shareNotice">
          分享
        </van-button>
        <van-button 
          type="primary" 
          @click="reportIssue"
        >
          举报问题
        </van-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-wrapper" size="24px">
      加载中...
    </van-loading>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showShareSheet, showDialog } from 'vant'

const route = useRoute()
const router = useRouter()

// 响应式数据
const noticeDetail = ref({})
const relatedNotices = ref([])
const activeNames = ref([])

// 方法
const fetchNoticeDetail = async () => {
  try {
    const noticeId = route.params.id
    
    // TODO: 调用API获取公示详情
    // const response = await awardApi.getNoticeDetail(noticeId)
    // noticeDetail.value = response
    
    // 模拟数据
    noticeDetail.value = {
      id: noticeId,
      title: '2024年第一批购房补贴发放公示',
      content: `
        <h2>公示说明</h2>
        <p>根据《购房补贴管理办法》相关规定，经审核，以下人员符合购房补贴发放条件，现予以公示。</p>
        
        <h2>公示要求</h2>
        <p>1. 公示期为7个工作日</p>
        <p>2. 如有异议，请在公示期内实名举报</p>
        <p>3. 举报内容应真实有效，提供相关证据材料</p>
        
        <h2>发放标准</h2>
        <p>按照购房面积和价格，给予相应比例的补贴：</p>
        <ul>
          <li>90平方米以下：补贴房价的2%</li>
          <li>90-120平方米：补贴房价的1.5%</li>
          <li>120-144平方米：补贴房价的1%</li>
        </ul>
      `,
      categoryName: '购房补贴',
      status: 'ongoing',
      publishTime: '2024-01-15',
      viewCount: 856,
      isCollected: false,
      publicityPeriod: {
        start: '2024-01-15',
        end: '2024-01-22'
      },
      reportPhone: '12345',
      reportEmail: '<EMAIL>',
      awardList: [
        {
          name: '张三',
          reason: '首套房购买，面积85㎡',
          amount: '6万元'
        },
        {
          name: '李四',
          reason: '首套房购买，面积110㎡',
          amount: '4.5万元'
        },
        {
          name: '王五',
          reason: '首套房购买，面积130㎡',
          amount: '3万元'
        }
      ]
    }

    // 获取相关公示
    relatedNotices.value = [
      {
        id: 2,
        title: '高层次人才奖励资金发放公示',
        publishTime: '2024-01-10'
      },
      {
        id: 3,
        title: '创业扶持资金补贴名单公示',
        publishTime: '2024-01-08'
      }
    ]
  } catch (error) {
    console.error('获取公示详情失败:', error)
    showToast('获取详情失败')
  }
}

const callPhone = (phone) => {
  window.location.href = `tel:${phone}`
}

const sendEmail = (email) => {
  window.location.href = `mailto:${email}`
}

const goToNotice = (notice) => {
  router.push({
    name: 'award-notice-detail',
    params: { id: notice.id }
  })
}

const toggleCollect = async () => {
  try {
    // TODO: 调用收藏/取消收藏API
    noticeDetail.value.isCollected = !noticeDetail.value.isCollected
    showToast(noticeDetail.value.isCollected ? '收藏成功' : '取消收藏')
  } catch (error) {
    showToast('操作失败')
  }
}

const shareNotice = () => {
  showShareSheet({
    title: '分享公示',
    options: [
      { name: '微信', icon: 'wechat' },
      { name: '朋友圈', icon: 'wechat-moments' },
      { name: '复制链接', icon: 'link' }
    ],
    onSelect: (option) => {
      showToast(`分享到${option.name}`)
    }
  })
}

const reportIssue = () => {
  showDialog({
    title: '举报问题',
    message: '如发现公示内容有误或存在违规情况，请拨打举报电话或发送邮件举报。',
    confirmButtonText: '拨打电话',
    cancelButtonText: '发送邮件'
  }).then(() => {
    callPhone(noticeDetail.value.reportPhone)
  }).catch(() => {
    sendEmail(noticeDetail.value.reportEmail)
  })
}

// 生命周期
onMounted(() => {
  fetchNoticeDetail()
})
</script>

<style lang="scss" scoped>
.award-notice-detail-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
  padding-bottom: 80px; /* 为底部操作栏预留空间 */
}

.content {
  background: white;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.notice-header {
  padding: 20px;
  border-bottom: 1px solid var(--van-border-color);
}

.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 12px;
  color: var(--van-text-color);
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.notice-period {
  margin-top: 12px;
}

.notice-content {
  padding: 20px;
}

.content-html {
  line-height: 1.6;
  color: var(--van-text-color);
  
  :deep(h2) {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 12px;
    color: var(--van-text-color);
  }
  
  :deep(p) {
    margin-bottom: 12px;
  }
  
  :deep(ul) {
    padding-left: 20px;
    margin-bottom: 12px;
  }
  
  :deep(li) {
    margin-bottom: 6px;
  }
}

.award-list {
  max-height: 300px;
  overflow-y: auto;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--van-border-color);
  display: flex;
  gap: 12px;
  align-items: center;
  
  .van-button {
    flex: 1;
  }
  
  .is-collected {
    color: var(--van-primary-color);
  }
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

<route lang="json5">
{
  name: 'award-notice-detail',
  meta: {
    title: '公示详情'
  }
}
</route>
