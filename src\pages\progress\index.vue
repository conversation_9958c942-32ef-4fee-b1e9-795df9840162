<!-- 进度页面 -->
<template>
  <div class="progress-page">
    <div class="content">
      <van-empty 
        v-if="progressList.length === 0 && !loading"
        description="暂无申报记录" 
      />
      
      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <van-cell
          v-for="item in progressList"
          :key="item.id"
          :title="item.title"
          :label="item.createTime"
          :value="item.status"
          is-link
          @click="goToDetail(item)"
        >
          <template #icon>
            <van-icon name="orders-o" size="20" />
          </template>
        </van-cell>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const progressList = ref([])

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取进度列表
    // const response = await progressApi.getProgressList()
    // progressList.value = response.list
    
    // 模拟数据
    progressList.value = [
      {
        id: 1,
        title: '购房补贴申请',
        createTime: '2024-01-15 10:30:00',
        status: '审核中'
      },
      {
        id: 2,
        title: '人才补贴申请',
        createTime: '2024-01-10 14:20:00',
        status: '已通过'
      }
    ]
    finished.value = true
  } finally {
    loading.value = false
  }
}

const onLoad = () => {
  fetchData()
}

const goToDetail = (item) => {
  router.push({
    name: 'progress-detail',
    params: { id: item.id }
  })
}

// 生命周期
onMounted(() => {
  fetchData()
})

onActivated(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.progress-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}
</style>

<route lang="json5">
{
  name: 'progress',
  meta: {
    title: '进度'
  }
}
</route>
