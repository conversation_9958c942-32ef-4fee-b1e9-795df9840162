<!-- 进度详情页 -->
<template>
  <div class="progress-detail-page">
    <div class="content" v-if="progressDetail.id">
      <!-- 申请状态卡片 -->
      <div class="status-card">
        <div class="status-header">
          <h2 class="title">{{ progressDetail.title }}</h2>
          <van-tag 
            :type="getStatusType(progressDetail.status)" 
            size="large"
          >
            {{ progressDetail.statusText }}
          </van-tag>
        </div>
        <div class="status-info">
          <p>申请时间：{{ progressDetail.createTime }}</p>
          <p v-if="progressDetail.completeTime">完成时间：{{ progressDetail.completeTime }}</p>
        </div>
      </div>

      <!-- Tab切换 -->
      <van-tabs v-model:active="activeTab" sticky>
        <!-- 申报详情 -->
        <van-tab title="申报详情" name="detail">
          <div class="tab-content">
            <!-- 申请人信息 -->
            <van-cell-group title="申请人信息">
              <van-cell title="姓名" :value="progressDetail.applicant.name" />
              <van-cell title="身份证号" :value="progressDetail.applicant.idCard" />
              <van-cell title="联系电话" :value="progressDetail.applicant.phone" />
              <van-cell title="联系地址" :value="progressDetail.applicant.address" />
            </van-cell-group>

            <!-- 房屋信息 -->
            <van-cell-group title="房屋信息">
              <van-cell title="房屋地址" :value="progressDetail.house.address" />
              <van-cell title="房屋面积" :value="progressDetail.house.area + '㎡'" />
              <van-cell title="成交价格" :value="progressDetail.house.price + '万元'" />
              <van-cell title="购房时间" :value="progressDetail.house.buyTime" />
            </van-cell-group>

            <!-- 补贴信息 -->
            <van-cell-group title="补贴信息">
              <van-cell title="补贴类型" :value="progressDetail.subsidy.type" />
              <van-cell title="补贴金额" :value="progressDetail.subsidy.amount + '元'" />
              <van-cell title="补贴比例" :value="progressDetail.subsidy.rate" />
            </van-cell-group>

            <!-- 收款账户 -->
            <van-cell-group title="收款账户">
              <van-cell title="开户银行" :value="progressDetail.account.bankName" />
              <van-cell title="账户姓名" :value="progressDetail.account.accountName" />
              <van-cell title="银行账号" :value="progressDetail.account.accountNumber" />
            </van-cell-group>

            <!-- 申报材料 -->
            <van-cell-group title="申报材料">
              <van-cell
                v-for="material in progressDetail.materials"
                :key="material.id"
                :title="material.name"
                :value="material.status"
                is-link
                @click="previewMaterial(material)"
              >
                <template #icon>
                  <van-icon name="description" />
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        </van-tab>

        <!-- 办理进度 -->
        <van-tab title="办理进度" name="progress">
          <div class="tab-content">
            <!-- 进度时间线 -->
            <van-steps direction="vertical" :active="currentStepIndex">
              <van-step
                v-for="(step, index) in progressDetail.steps"
                :key="index"
              >
                <template #active-icon>
                  <van-icon name="success" />
                </template>
                <template #inactive-icon>
                  <van-icon name="clock-o" />
                </template>
                <div class="step-content">
                  <h3 class="step-title">{{ step.title }}</h3>
                  <p class="step-desc">{{ step.description }}</p>
                  <p class="step-time" v-if="step.time">{{ step.time }}</p>
                  <p class="step-operator" v-if="step.operator">操作人：{{ step.operator }}</p>
                  <div class="step-remark" v-if="step.remark">
                    <van-tag type="warning" size="small">备注</van-tag>
                    <span>{{ step.remark }}</span>
                  </div>
                </div>
              </van-step>
            </van-steps>

            <!-- 预计完成时间 -->
            <van-cell-group title="预计信息" v-if="progressDetail.estimate">
              <van-cell 
                title="预计完成时间" 
                :value="progressDetail.estimate.completeTime" 
              />
              <van-cell 
                title="剩余工作日" 
                :value="progressDetail.estimate.remainDays + '天'" 
              />
            </van-cell-group>
          </div>
        </van-tab>
      </van-tabs>

      <!-- 底部操作 -->
      <div class="bottom-actions">
        <van-button 
          v-if="progressDetail.canCancel"
          type="danger"
          @click="cancelApplication"
        >
          撤销申请
        </van-button>
        <van-button 
          v-if="progressDetail.canModify"
          type="warning"
          @click="modifyApplication"
        >
          修改申请
        </van-button>
        <van-button 
          type="primary"
          @click="contactService"
        >
          联系客服
        </van-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-wrapper" size="24px">
      加载中...
    </van-loading>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const route = useRoute()
const router = useRouter()

// 响应式数据
const activeTab = ref('detail')
const progressDetail = ref({})

// 计算属性
const currentStepIndex = computed(() => {
  if (!progressDetail.value.steps) return 0
  return progressDetail.value.steps.findIndex(step => step.status === 'current')
})

// 方法
const fetchProgressDetail = async () => {
  try {
    const progressId = route.params.id
    
    // TODO: 调用API获取进度详情
    // const response = await progressApi.getProgressDetail(progressId)
    // progressDetail.value = response
    
    // 模拟数据
    progressDetail.value = {
      id: progressId,
      title: '购房补贴申请',
      status: 'reviewing',
      statusText: '审核中',
      createTime: '2024-01-15 10:30:00',
      completeTime: '',
      canCancel: true,
      canModify: false,
      applicant: {
        name: '张三',
        idCard: '330***********1234',
        phone: '138****8888',
        address: '某某市某某区某某街道123号'
      },
      house: {
        address: '某某小区1号楼101室',
        area: '120',
        price: '300',
        buyTime: '2024-01-10'
      },
      subsidy: {
        type: '首套房购房补贴',
        amount: '45000',
        rate: '房价的1.5%'
      },
      account: {
        bankName: '中国工商银行',
        accountName: '张三',
        accountNumber: '6222***********1234'
      },
      materials: [
        {
          id: 1,
          name: '身份证',
          status: '已审核',
          url: '/files/idcard.pdf'
        },
        {
          id: 2,
          name: '购房合同',
          status: '已审核',
          url: '/files/contract.pdf'
        },
        {
          id: 3,
          name: '不动产证',
          status: '审核中',
          url: '/files/property.pdf'
        }
      ],
      steps: [
        {
          title: '提交申请',
          description: '申请材料已提交，等待初审',
          time: '2024-01-15 10:30:00',
          operator: '系统',
          status: 'completed'
        },
        {
          title: '材料初审',
          description: '相关部门正在进行材料初审',
          time: '2024-01-16 09:00:00',
          operator: '审核员A',
          status: 'completed'
        },
        {
          title: '实地核查',
          description: '工作人员进行实地核查',
          time: '2024-01-18 14:00:00',
          operator: '核查员B',
          status: 'current',
          remark: '已预约上门核查时间'
        },
        {
          title: '审核通过',
          description: '审核完成，等待发放',
          time: '',
          operator: '',
          status: 'pending'
        },
        {
          title: '补贴发放',
          description: '补贴资金已发放到账',
          time: '',
          operator: '',
          status: 'pending'
        }
      ],
      estimate: {
        completeTime: '2024-01-30',
        remainDays: 8
      }
    }
  } catch (error) {
    console.error('获取进度详情失败:', error)
    showToast('获取详情失败')
  }
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'default',
    'reviewing': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'completed': 'success'
  }
  return statusMap[status] || 'default'
}

const previewMaterial = (material) => {
  showToast(`预览 ${material.name}`)
  // TODO: 实现材料预览功能
}

const cancelApplication = async () => {
  try {
    await showConfirmDialog({
      title: '确认撤销',
      message: '确定要撤销此申请吗？撤销后无法恢复。'
    })
    
    // TODO: 调用撤销申请API
    showToast('申请已撤销')
    router.back()
  } catch (error) {
    // 用户取消
  }
}

const modifyApplication = () => {
  router.push({
    name: 'subsidy-apply',
    query: { id: progressDetail.value.id }
  })
}

const contactService = () => {
  showToast('客服电话：400-123-4567')
}

// 生命周期
onMounted(() => {
  fetchProgressDetail()
})
</script>

<style lang="scss" scoped>
.progress-detail-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
  padding-bottom: 80px; /* 为底部操作栏预留空间 */
}

.content {
  padding-bottom: 16px;
}

.status-card {
  background: white;
  margin: 16px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: var(--van-text-color);
}

.status-info {
  font-size: 14px;
  color: var(--van-text-color-2);
  
  p {
    margin: 4px 0;
  }
}

.tab-content {
  padding: 16px;
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.step-content {
  padding-left: 12px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--van-text-color);
}

.step-desc {
  font-size: 14px;
  color: var(--van-text-color-2);
  margin-bottom: 4px;
}

.step-time {
  font-size: 12px;
  color: var(--van-text-color-3);
  margin-bottom: 4px;
}

.step-operator {
  font-size: 12px;
  color: var(--van-text-color-3);
  margin-bottom: 8px;
}

.step-remark {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--van-border-color);
  display: flex;
  gap: 12px;
  
  .van-button {
    flex: 1;
  }
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

<route lang="json5">
{
  name: 'progress-detail',
  meta: {
    title: '申请详情'
  }
}
</route>
