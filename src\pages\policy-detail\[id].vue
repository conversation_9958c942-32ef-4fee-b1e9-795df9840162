<!-- 政策详情页 -->
<template>
  <div class="policy-detail-page">
    <div class="content" v-if="policyDetail.id">
      <!-- 文章头部 -->
      <div class="article-header">
        <h1 class="title">{{ policyDetail.title }}</h1>
        <div class="meta-info">
          <span class="publish-time">{{ policyDetail.publishTime }}</span>
          <span class="view-count">
            <van-icon name="eye-o" />
            {{ policyDetail.viewCount }}
          </span>
        </div>
        <div class="tags">
          <van-tag type="primary" size="small">{{ policyDetail.categoryName }}</van-tag>
          <van-tag v-if="policyDetail.isHot" type="danger" size="small">热门</van-tag>
        </div>
      </div>

      <!-- 文章内容 -->
      <div class="article-content">
        <div class="content-html" v-html="policyDetail.content"></div>
      </div>

      <!-- 附件下载 -->
      <van-cell-group v-if="policyDetail.attachments && policyDetail.attachments.length > 0" title="相关附件">
        <van-cell
          v-for="file in policyDetail.attachments"
          :key="file.id"
          :title="file.name"
          :label="file.size"
          icon="description"
          is-link
          @click="downloadFile(file)"
        />
      </van-cell-group>

      <!-- 相关政策推荐 -->
      <van-cell-group v-if="relatedPolicies.length > 0" title="相关政策">
        <van-cell
          v-for="item in relatedPolicies"
          :key="item.id"
          :title="item.title"
          :label="item.publishTime"
          is-link
          @click="goToPolicy(item)"
        />
      </van-cell-group>

      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <van-button 
          type="primary" 
          size="large" 
          @click="goToApply"
          v-if="policyDetail.canApply"
        >
          立即申请
        </van-button>
        <div class="action-buttons">
          <van-button 
            icon="star-o" 
            :class="{ 'is-collected': policyDetail.isCollected }"
            @click="toggleCollect"
          >
            {{ policyDetail.isCollected ? '已收藏' : '收藏' }}
          </van-button>
          <van-button icon="share-o" @click="sharePolicy">
            分享
          </van-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-wrapper" size="24px">
      加载中...
    </van-loading>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showShareSheet } from 'vant'

const route = useRoute()
const router = useRouter()

// 响应式数据
const policyDetail = ref({})
const relatedPolicies = ref([])

// 方法
const fetchPolicyDetail = async () => {
  try {
    const policyId = route.params.id
    
    // TODO: 调用API获取政策详情
    // const response = await policyApi.getPolicyDetail(policyId)
    // policyDetail.value = response
    
    // 模拟数据
    policyDetail.value = {
      id: policyId,
      title: '2024年购房补贴政策实施细则',
      content: `
        <h2>一、政策背景</h2>
        <p>为进一步促进房地产市场健康发展，支持刚性和改善性住房需求，现制定本实施细则。</p>
        
        <h2>二、补贴对象</h2>
        <p>符合以下条件的购房人可申请购房补贴：</p>
        <ul>
          <li>在本市购买首套住房的家庭</li>
          <li>购房时间在2024年1月1日至2024年12月31日期间</li>
          <li>购房面积不超过144平方米</li>
          <li>家庭年收入不超过当地平均收入的3倍</li>
        </ul>
        
        <h2>三、补贴标准</h2>
        <p>根据购房面积和价格，给予不同标准的补贴：</p>
        <ul>
          <li>90平方米以下：补贴房价的2%</li>
          <li>90-120平方米：补贴房价的1.5%</li>
          <li>120-144平方米：补贴房价的1%</li>
        </ul>
        
        <h2>四、申请流程</h2>
        <p>1. 在线提交申请材料</p>
        <p>2. 相关部门审核</p>
        <p>3. 公示无异议后发放补贴</p>
      `,
      categoryName: '购房补贴',
      publishTime: '2024-01-15',
      viewCount: 1234,
      isHot: true,
      canApply: true,
      isCollected: false,
      attachments: [
        {
          id: 1,
          name: '购房补贴申请表.pdf',
          size: '256KB',
          url: '/files/application-form.pdf'
        },
        {
          id: 2,
          name: '政策解读文件.doc',
          size: '128KB',
          url: '/files/policy-guide.doc'
        }
      ]
    }

    // 获取相关政策
    relatedPolicies.value = [
      {
        id: 2,
        title: '高层次人才购房优惠政策',
        publishTime: '2024-01-10'
      },
      {
        id: 3,
        title: '首套房契税减免政策解读',
        publishTime: '2024-01-08'
      }
    ]
  } catch (error) {
    console.error('获取政策详情失败:', error)
    showToast('获取详情失败')
  }
}

const downloadFile = (file) => {
  // TODO: 实现文件下载
  showToast(`下载 ${file.name}`)
}

const goToPolicy = (policy) => {
  router.push({
    name: 'policy-detail',
    params: { id: policy.id }
  })
}

const goToApply = () => {
  router.push({ name: 'subsidy-apply' })
}

const toggleCollect = async () => {
  try {
    // TODO: 调用收藏/取消收藏API
    policyDetail.value.isCollected = !policyDetail.value.isCollected
    showToast(policyDetail.value.isCollected ? '收藏成功' : '取消收藏')
  } catch (error) {
    showToast('操作失败')
  }
}

const sharePolicy = () => {
  showShareSheet({
    title: '分享政策',
    options: [
      { name: '微信', icon: 'wechat' },
      { name: '朋友圈', icon: 'wechat-moments' },
      { name: '复制链接', icon: 'link' }
    ],
    onSelect: (option) => {
      showToast(`分享到${option.name}`)
    }
  })
}

// 生命周期
onMounted(() => {
  fetchPolicyDetail()
})
</script>

<style lang="scss" scoped>
.policy-detail-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
  padding-bottom: 80px; /* 为底部操作栏预留空间 */
}

.content {
  background: white;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.article-header {
  padding: 20px;
  border-bottom: 1px solid var(--van-border-color);
}

.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 12px;
  color: var(--van-text-color);
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tags {
  display: flex;
  gap: 8px;
}

.article-content {
  padding: 20px;
}

.content-html {
  line-height: 1.6;
  color: var(--van-text-color);
  
  :deep(h2) {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 12px;
    color: var(--van-text-color);
  }
  
  :deep(p) {
    margin-bottom: 12px;
  }
  
  :deep(ul) {
    padding-left: 20px;
    margin-bottom: 12px;
  }
  
  :deep(li) {
    margin-bottom: 6px;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--van-border-color);
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  
  .van-button {
    flex: 1;
  }
  
  .is-collected {
    color: var(--van-primary-color);
  }
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

<route lang="json5">
{
  name: 'policy-detail',
  meta: {
    title: '政策详情'
  }
}
</route>
