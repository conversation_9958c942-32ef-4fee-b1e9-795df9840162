<!-- 申报指南列表页 -->
<template>
  <div class="apply-guide-page">
    <div class="content">
      <!-- 搜索栏 -->
      <van-search
        v-model="searchKeyword"
        placeholder="搜索申报指南"
        @search="onSearch"
        @clear="onClear"
      />

      <!-- 指南分类 -->
      <van-tabs v-model:active="activeCategory" @change="onCategoryChange">
        <van-tab 
          v-for="category in categoryList" 
          :key="category.id"
          :title="category.name"
        />
      </van-tabs>

      <!-- 指南列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        class="guide-list"
      >
        <van-card
          v-for="item in guideList"
          :key="item.id"
          :title="item.title"
          :desc="item.summary"
          :thumb="item.cover"
          @click="goToDetail(item)"
        >
          <template #tags>
            <van-tag type="primary" size="small">{{ item.categoryName }}</van-tag>
            <van-tag v-if="item.isRecommend" type="success" size="small">推荐</van-tag>
          </template>
          <template #footer>
            <div class="card-footer">
              <span class="publish-time">{{ item.publishTime }}</span>
              <div class="actions">
                <van-icon name="eye-o" />
                <span>{{ item.viewCount }}</span>
              </div>
            </div>
          </template>
        </van-card>
      </van-list>

      <!-- 空状态 -->
      <van-empty 
        v-if="guideList.length === 0 && !loading"
        description="暂无申报指南" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeCategory = ref(0)
const guideList = ref([])
const categoryList = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '购房补贴' },
  { id: 2, name: '人才申报' },
  { id: 3, name: '创业扶持' },
  { id: 4, name: '税收优惠' }
])

// 分页参数
const pageParams = ref({
  current: 1,
  size: 10
})

// 方法
const fetchGuideList = async (reset = false) => {
  if (reset) {
    pageParams.value.current = 1
    guideList.value = []
    finished.value = false
  }

  loading.value = true
  try {
    // TODO: 调用API获取申报指南列表
    // const response = await guideApi.getGuideList({
    //   ...pageParams.value,
    //   keyword: searchKeyword.value,
    //   categoryId: categoryList.value[activeCategory.value].id
    // })
    
    // 模拟数据
    const mockData = [
      {
        id: 1,
        title: '购房补贴申报完整指南',
        summary: '详细介绍购房补贴的申报条件、所需材料、申报流程等信息...',
        cover: '/src/assets/bg.png',
        categoryName: '购房补贴',
        isRecommend: true,
        publishTime: '2024-01-15',
        viewCount: 2156
      },
      {
        id: 2,
        title: '高层次人才认定申报指南',
        summary: '高层次人才认定的标准、申报材料清单、评审流程说明...',
        cover: '/src/assets/bg.png',
        categoryName: '人才申报',
        isRecommend: false,
        publishTime: '2024-01-12',
        viewCount: 1834
      },
      {
        id: 3,
        title: '创业扶持资金申请指南',
        summary: '创业项目扶持资金的申请条件、评审标准、申报时间安排...',
        cover: '/src/assets/bg.png',
        categoryName: '创业扶持',
        isRecommend: true,
        publishTime: '2024-01-10',
        viewCount: 1456
      },
      {
        id: 4,
        title: '小微企业税收优惠申报指南',
        summary: '小微企业享受税收优惠政策的条件、申报方式、注意事项...',
        cover: '/src/assets/bg.png',
        categoryName: '税收优惠',
        isRecommend: false,
        publishTime: '2024-01-08',
        viewCount: 1123
      }
    ]

    if (reset) {
      guideList.value = mockData
    } else {
      guideList.value.push(...mockData)
    }

    pageParams.value.current++
    
    // 模拟没有更多数据
    if (pageParams.value.current > 2) {
      finished.value = true
    }
  } catch (error) {
    console.error('获取申报指南列表失败:', error)
  } finally {
    loading.value = false
  }
}

const onLoad = () => {
  fetchGuideList()
}

const onSearch = () => {
  fetchGuideList(true)
}

const onClear = () => {
  searchKeyword.value = ''
  fetchGuideList(true)
}

const onCategoryChange = () => {
  fetchGuideList(true)
}

const goToDetail = (item) => {
  router.push({
    name: 'guide-detail',
    params: { id: item.id }
  })
}

// 生命周期
onMounted(() => {
  fetchGuideList(true)
})

onActivated(() => {
  fetchGuideList(true)
})
</script>

<style lang="scss" scoped>
.apply-guide-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding-bottom: 16px;
}

.van-search {
  padding: 16px;
  background: white;
}

.guide-list {
  padding: 0 16px;
  
  .van-card {
    margin-bottom: 16px;
    
    :deep(.van-card__content) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--van-text-color-2);
}

.actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.publish-time {
  color: var(--van-text-color-3);
}
</style>

<route lang="json5">
{
  name: 'apply-guide',
  meta: {
    title: '申报指南'
  }
}
</route>
